use crate::database::common::get_db;
use crate::raydium::pool::*;
use std::collections::HashMap;
use std::error::Error;

pub const CF_NAME: &str = "strategy.raydium";

const USDC_ADDRESS: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
const USDT_ADDRESS: &str = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";

pub async fn update_database() -> Result<(), Box<dyn Error>> {
    todo!()
}

#[allow(unused)]
async fn update_usdt_data(token: String) -> Result<(), Box<dyn Error>> {
    let result = get_info_by_mint(
        token.clone(),
        None,
        PoolType::All,
        PoolSortField::Liquidity,
        SortType::Desc,
        100,
        1,
    )
    .await?;
    let db = get_db();
    let mut new_tokens = vec![];
    let cf = db
        .cf_handle(CF_NAME)
        .ok_or(format!("Column family {} not found", CF_NAME))?;
    for pool in result.data.data {
        let (mint_token, mint_other) = if pool.mint_a.address == token {
            (&pool.mint_a, &pool.mint_b)
        } else {
            (&pool.mint_b, &pool.mint_a)
        };
        let mint_token_key = get_token_key(&mint_token.address);
        let mint_other_key = get_token_key(&mint_other.address);
        // add mint token info
        if db.get_cf(cf, &mint_token_key).is_err() {
            db.put_cf(
                cf,
                &mint_token_key,
                serde_json::to_string(mint_token)?.as_bytes(),
            )?;
        }
        // add other token info
        if db.get_cf(cf, &mint_other_key).is_err() {
            db.put_cf(
                cf,
                &mint_other_key,
                serde_json::to_string(mint_other)?.as_bytes(),
            )?;
            new_tokens.push(mint_other.address.clone());
        }
        // add pool info
        let pool_key = get_pool_key(&pool.id);
        if db.get_cf(cf, &pool_key).is_err() {
            db.put_cf(cf, &pool_key, serde_json::to_string(&pool)?.as_bytes())?;
        }
        // add pair info
        let pair_key = get_pair_key(&mint_token.address, &mint_other.address);
        if db.get_cf(cf, &pair_key).is_err() {
            db.put_cf(cf, &pair_key, pool.id.as_bytes())?;
        }
        let pair_key = get_pair_key(&mint_other.address, &mint_token.address);
        if db.get_cf(cf, &pair_key).is_err() {
            db.put_cf(cf, &pair_key, pool.id.as_bytes())?;
        }
    }
    for i in 0..new_tokens.len() {
        for j in i + 1..new_tokens.len() {
            let token1 = new_tokens[i].clone();
            let token2 = new_tokens[j].clone();
            let result = get_info_by_mint(
                token1.clone(),
                Some(token2.clone()),
                PoolType::All,
                PoolSortField::Liquidity,
                SortType::Desc,
                1,
                1,
            )
            .await?;
            if result.data.count == 1 {
                let pool = &result.data.data[0];
                let mint_a = &pool.mint_a;
                let mint_b = &pool.mint_b;
                // add pool info
                let pool_key = get_pool_key(&pool.id);
                if db.get_cf(cf, &pool_key).is_err() {
                    db.put_cf(cf, &pool_key, serde_json::to_string(pool)?.as_bytes())?;
                }
                // add pair info
                let pair_key = get_pair_key(&mint_a.address, &mint_b.address);
                if db.get_cf(cf, &pair_key).is_err() {
                    db.put_cf(cf, &pair_key, pool.id.as_bytes())?;
                }
                let pair_key = get_pair_key(&mint_b.address, &mint_a.address);
                if db.get_cf(cf, &pair_key).is_err() {
                    db.put_cf(cf, &pair_key, pool.id.as_bytes())?;
                }
            }
        }
        // 我需要dbkai qi
    }
    Ok(())
}

// value will be TokenInfo
fn get_token_key(address: &str) -> String {
    format!("token/{}", address)
}

// value will be PoolInfo
fn get_pool_key(id: &str) -> String {
    format!("pool/{}", id)
}

// value will be pool_id
fn get_pair_key(address1: &str, address2: &str) -> String {
    format!("pair/{}:{}", address1, address2)
}

pub async fn find_triangular_arbitrage() {
    // 调用get_info_by_mint接口
    let result = get_info_by_mint(
        USDT_ADDRESS.to_string(),
        None,
        PoolType::All,
        PoolSortField::Liquidity,
        SortType::Desc,
        10,
        1,
    )
    .await
    .unwrap();
    // building token map token_address -> token_symbol,
    // which makes it easier to print out the token symbol
    let mut tokens = HashMap::new();
    let mut prices = HashMap::new();
    for pool in result.data.data {
        // 获取池子中的两个代币
        let token_a = pool.mint_a;
        let token_b = pool.mint_b;
        let price = pool.price;
        tokens
            .entry(token_a.address.clone())
            .or_insert(token_a.symbol.clone());
        tokens
            .entry(token_b.address.clone())
            .or_insert(token_b.symbol.clone());
        prices.insert(format!("{}:{}", token_a.address, token_b.address), price);
        prices.insert(format!("{}:{}", token_a.symbol, token_b.symbol), price);
        let reverse_price = 1.0 / price;
        prices.insert(
            format!("{}:{}", token_b.address, token_a.address),
            reverse_price,
        );
        prices.insert(
            format!("{}:{}", token_b.symbol, token_a.symbol),
            reverse_price,
        );
        // 打印代币信息
        println!(
            "{}({}):{}({}) {}",
            token_a.symbol, token_a.address, token_b.symbol, token_b.address, price
        );
    }
    // then find triangular arbitrage
    let mut all_tokens = vec![];
    for token in tokens.keys() {
        if token != USDT_ADDRESS {
            all_tokens.push(token.clone());
        }
    }
    // println!("response: {:#?}", response);
    for i in 0..all_tokens.len() {
        for j in i + 1..all_tokens.len() {
            let token_b = all_tokens[i].clone();
            let token_c = all_tokens[j].clone();
            let symbol_b = tokens.get(&token_b).unwrap();
            let symbol_c = tokens.get(&token_c).unwrap();
            // get price
            let response = get_info_by_mint(
                token_b.clone(),
                Some(token_c.clone()),
                PoolType::All,
                PoolSortField::Liquidity,
                SortType::Desc,
                1,
                1,
            )
            .await;
            if let Ok(response) = response {
                // println!("response: {:#?}", response);
                if response.data.count == 1 {
                    let info = &response.data.data[0];
                    let price = info.price;
                    let reverse_price = 1.0 / price;
                    let mint_a = info.mint_a.address.clone();
                    let mint_b = info.mint_b.address.clone();
                    prices.insert(format!("{}:{}", mint_a, mint_b), price);
                    prices.insert(format!("{}:{}", mint_b, mint_a), reverse_price);
                }
            }

            if let Some(profit) =
                can_triangular_arbitrage(prices.clone(), token_b.clone(), token_c.clone())
            {
                if profit < 0.0 {
                    println!(
                        "Found triangular arbitrage: USDC -> {} -> {} -> USDC with profit {}%",
                        symbol_c,
                        symbol_b,
                        -profit * 100.0
                    );
                } else if profit > 0.0 {
                    println!(
                        "Found triangular arbitrage: USDC -> {} -> {} -> USDC with profit {}%",
                        symbol_b,
                        symbol_c,
                        profit * 100.0
                    );
                } else {
                    println!("No triangular arbitrage: ({} -> {})", symbol_b, symbol_c,);
                }
            } else {
                println!(
                    "Can not calculate triangular arbitrage: ({} -> {})",
                    symbol_b, symbol_c,
                );
            }
        }
    }
}

// token_a is always usdc
// price is token_1 / token_2, for example:
// if 1 sol == 180 usdc, then the key will be {sol}:{usdc}, and the value will be 180
// 1a = xb, so a/b = x, which means x = price[{token_a}:{token_b}]
// 1b = yc, so b/c = y, which means y = price[{token_b}:{token_c}]
// 1c = za, so c/a = z, which means z = price[{token_c}:{token_a}]
// if xyz == 1, return Some(0)
// if xyz > 1, return Some(xyz - 1), a->b->c->a
// if xyz < 1, return Some(1/xyz - 1), a->c->b->a
// if x, y or z is not exist, return None
fn can_triangular_arbitrage(
    prices: HashMap<String, f64>,
    token_b: String,
    token_c: String,
) -> Option<f64> {
    let token_a = USDT_ADDRESS.to_string();
    let x = *prices.get(&format!("{}:{}", token_a, token_b))?;
    let y = *prices.get(&format!("{}:{}", token_b, token_c))?;
    let z = *prices.get(&format!("{}:{}", token_c, token_a))?;
    let xyz = x * y * z;
    println!("x is {}, y is {}, z is {}", x, y, z);
    if xyz > 1.0 {
        return Some(xyz - 1.0);
    } else if xyz < 1.0 {
        return Some(1.0 - 1.0 / xyz);
    }
    None
}
