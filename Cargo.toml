[package]
name = "solquant"
version = "0.1.0"
edition = "2024"

[dependencies]
solana-client = "2.3.5"
solana-sdk = "2.3.1"
serde = { version = "1.0", features = ["derive"] }
toml = "0.8"
tokio = "1.46.1"
reqwest = { version = "0.12.22", features = ["json"] }
serde_json = "1.0.141"
once_cell = "1.21.3"
futures-util = "0.3.31"
tokio-tungstenite = { version = "0.21", features = ["rustls-tls-webpki-roots"] }
tungstenite = "0.21.0"
tracing = "0.1"
tracing-subscriber = "0.3"
rocksdb = "0.22"

[dev-dependencies]
tempfile = "3.0"
